<template>
	<div :style="containerStyle" class="area-container">
	    <textarea 
			:placeholder="xPlaceHolder" 
			v-model="xValue" 
			:style="textareaStyle"
			:class="['textarea-base', currentFocus == 1 ? 'textarea-focus' : '']"
			@focus="onFocus()" 
			@blur="onBlur()"
			:maxlength="maxLength"
			:ComponentPath="props.ComponentPath"
		></textarea>
		<img src="../../assets/circle-close.png" class="clear-button" v-if="xValue !=''" @click.stop="clearTextarea()" />
	</div>
</template>

<script lang="ts" setup>
	import { reactive, inject, onMounted, useAttrs, ref, watch, computed } from 'vue';
	import { useComponent } from '../../frame/useComponent.js'
	
	// 使用更灵活的类型定义
	interface DomItemType {
		css?: Record<string, any>;
		value?: any;
		placeholder?: string;
		onChange?: any;
		id?: string | number;
		maxLength?: number;
		PageEvents?: {
			onFocus?: any;
			onBlur?: any;
			[key: string]: any;
		};
		[key: string]: any;
	}

	// 定义Props类型
	interface Props {
		DomItem?: DomItemType;
		[key: string]: any;
	}

	const props = useAttrs() as Props;
	const { xRefCss, xOnClick, xRefValue, xSetValue, xOnFocus, xOnBlur } = useComponent(props);
	
	const domItem = computed(() => props.DomItem as DomItemType);
	
	// CSS属性
	const cssProps = computed(() => xRefCss(props.DomItem['css']) || {});
	
	// 最外层容器样式（处理位置和尺寸相关属性）
	const containerStyle = computed(() => {
		const allCss = cssProps.value;
		// 保留位置和尺寸相关属性
		const style = {
			position: allCss['position'] || 'relative',
			top: allCss['top'],
			left: allCss['left'],
			right: allCss['right'],
			bottom: allCss['bottom'],
			zIndex: allCss['zIndex'],
			width: allCss['width'],
			height: allCss['height']
		};
		return style;
	});
	
	// 获取文本域样式（除了getContainerStyle中的属性外，其余全部给textarea）
	const textareaStyle = computed(() => {
		// 复制一份cssProps
		const style = { ...cssProps.value } as Record<string, any>;
		
		// 移除位置和尺寸相关属性（这些属性已经在容器上设置了）
		const containerProps = ['position', 'top', 'left', 'right', 'bottom', 'zIndex', 'width', 'height'];
		containerProps.forEach(prop => {
			delete style[prop];
		});
		
		// 添加必要的定位样式，确保textarea填满容器
		style.position = 'absolute';
		style.top = '0';
		style.left = '0';
		style.width = '100%';
		style.height = '100%';
		
		return style;
	});
	
	
	let xValue = ref(xRefValue(props.DomItem['value']) || '');
	let xPlaceHolder = ref(props.DomItem['placeholder'] || '');
	let currentFocus = ref(0);
	
	// 字数限制
	const maxLength = computed(() => {
		return domItem.value.maxLength || 1000;
	});
	
	
	
	// 处理输入框值变化，使用xSetValue修改原始位置的值
	function updateValue(newValue) {
		// 检查是否有有效的value引用路径
		if (props.DomItem && props.DomItem['value']) {
			xSetValue(props.DomItem['value'], newValue)
		}
	}

	// 监听xValue变化，实时更新原始位置的值
	watch(xValue, (newValue) => {
		updateValue(newValue)
	})
	watch(()=>xRefValue(props.DomItem['value']),(nValue)=>{
		xValue.value = 	nValue
	})
	
	
	
	// 焦点事件
	function onFocus() {
		currentFocus.value = 1;

		// 触发PageEvents中的onFocus事件
		if (props.DomItem?.PageEvents?.onFocus) {
			xOnFocus(props.DomItem.PageEvents.onFocus);
		}
	}

	function onBlur() {
		currentFocus.value = 0;

		// 触发PageEvents中的onBlur事件
		if (props.DomItem?.PageEvents?.onBlur) {
			xOnBlur(props.DomItem.PageEvents.onBlur);
		}
	}
	
	function clearTextarea() {
		xValue.value = '';
	}
</script>

<script lang="ts">
export default {
  name: 'FWArea'
}
</script>

<style lang="scss" scoped>
.area-container {
	position: relative;
}

.textarea-base {
	font-family: inherit;
	line-height: 1.5;
	overflow: auto;
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	resize: none; /* 禁止用户拖动调整大小 */
	
	// 基础状态样式
	background-color: var(--basic-background-color);
	color: var(--basic-color);
	transform: var(--basic-transform);
	font-size: var(--basic-font-size);
	border: var(--basic-border);
	border-radius: var(--basic-border-radius);
	box-shadow: var(--basic-box-shadow);
	text-decoration: var(--basic-text-decoration);
	text-align: var(--basic-text-align);
	padding: var(--basic-padding);
	margin: var(--basic-margin);
	
	// hover状态样式
	&:hover {
		background-color: var(--hover-background-color);
		color: var(--hover-color);
		transform: var(--hover-transform);
		font-size: var(--hover-font-size);
		border: var(--hover-border);
		border-radius: var(--hover-border-radius);
		box-shadow: var(--hover-box-shadow);
		text-decoration: var(--hover-text-decoration);
		text-align: var(--hover-text-align);
		padding: var(--hover-padding);
		margin: var(--hover-margin);
	}
	
	// active状态样式（鼠标点击状态）
	&:active {
		background-color: var(--active-background-color);
		color: var(--active-color);
		transform: var(--active-transform);
		font-size: var(--active-font-size);
		border: var(--active-border);
		border-radius: var(--active-border-radius);
		box-shadow: var(--active-box-shadow);
		text-decoration: var(--active-text-decoration);
		text-align: var(--active-text-align);
		padding: var(--active-padding);
		margin: var(--active-margin);
	}
	
	// focus状态样式
	// &.textarea-focus {
	// 	background-color: var(--focus-background-color);
	// 	color: var(--focus-color);
	// 	transform: var(--focus-transform);
	// 	font-size: var(--focus-font-size);
	// 	border: var(--focus-border);
	// 	border-radius: var(--focus-border-radius);
	// 	box-shadow: var(--focus-box-shadow);
	// 	text-decoration: var(--focus-text-decoration);
	// 	text-align: var(--focus-text-align);
	// 	padding: var(--focus-padding);
	// 	margin: var(--focus-margin);
	// }
}

// 滚动条样式
textarea::-webkit-scrollbar {
	width: 6px;
}

textarea::-webkit-scrollbar-track {
	background: #f1f1f1;
	border-radius: 3px;
}

textarea::-webkit-scrollbar-thumb {
	background: #c1c1c1;
	border-radius: 3px;
}

textarea::-webkit-scrollbar-thumb:hover {
	background: #a8a8a8;
}

// 清除按钮样式
.clear-button {
	width: 20px;
	height: 20px;
	position: absolute;
	right: 10px;
	top: 10px;
	cursor: pointer;
	z-index: 1;
}

.clear-button:active {
	transform: scale(0.94);
}
</style>
