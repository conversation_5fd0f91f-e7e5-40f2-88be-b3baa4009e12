<template>
  <div :style="containerStyle" class="select-wrapper" :class="{ 'is-open': isOpen }">
    <div class="select-display" @click.stop="toggleDropdown" :style="displayStyle">
      <div class="select-display-label" :style="textStyle">{{ displayLabel }}</div>
      <el-icon class="select-arrow">
        <arrow-down />
      </el-icon>
    </div>
    <div v-if="isOpen" class="select-dropdown" :style="dropdownStyle">
      <div
        v-for="item in dataList"
        :key="item.value"
        :style="innerStyle"
        :class="['select-item', { selected: currentValue === item.value }]"
        @click="onSelect(item.value)"
      >
        {{ item.label }}
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, computed, useAttrs, onMounted, onUnmounted } from 'vue';
import { useComponent } from '../../frame/useComponent.js'
import { ArrowDown } from '@element-plus/icons-vue'

const props = useAttrs() as any
const { xRefCss, xRefValue, xSetValue } = useComponent(props)

// CSS属性
const cssProps = computed(() => {
  return xRefCss(props.DomItem['css']) || {}
})

// 处理数据源
const dataList = computed(() => {  
  if (props.DomItem.choose && props.DomItem.choose.dataExpression) {
    const dynamicData = xRefValue(props.DomItem.choose.dataExpression.trim());
    if (dynamicData && Array.isArray(dynamicData)) {
      return dynamicData;
    }
  }
  
  // 回退到静态数据列表
  return props.DomItem.choose.dataList || [];
})

// 选中的值
const currentValue = ref(props.DomItem.choose.currentValue)

// 下拉框是否打开
const isOpen = ref(false)

let iptValue = xRefValue(props.DomItem.value);

// 根据当前值显示的标签
const displayLabel = computed(() => {
  const item = dataList.value.find(item => item.value === currentValue.value)
  return item ? item.label : iptValue
})

// 容器样式
const containerStyle = computed(() => {
  return {
    width: cssProps.value['width'] || '100%',
    height: cssProps.value['height'] || 'auto',
    position: cssProps.value['position'] || 'static',
    top: cssProps.value['top'],
    left: cssProps.value['left'],
    right: cssProps.value['right'],
    bottom: cssProps.value['bottom'],
    zIndex: cssProps.value['zIndex'],
  }
})

// 下拉框样式
const innerStyle = computed(() => {
  return {
    width: cssProps.value['width'] || '100%',
    height: cssProps.value['height'] || 'auto',
    
    zIndex: cssProps.value['zIndex'],
  }
})

// 显示框样式
const displayStyle = computed(() => {
  return {
    ...cssProps.value,
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    boxSizing: 'border-box',
    height: '100%',
    padding: cssProps.value['padding'] || '0 10px',
    position: 'static',
  }
})

// 文本样式
const textStyle = computed(() => {
  return {
    textAlign: cssProps.value['textAlign'],
    fontWeight: cssProps.value['fontWeight'],
    fontSize: cssProps.value['fontSize'],
    fontStyle: cssProps.value['fontStyle'],
    lineHeight: cssProps.value['lineHeight']
  }
})

// 下拉列表样式
const dropdownStyle = computed(() => {
  return {
    width: '100%',
    backgroundColor: cssProps.value['backgroundColor'] || '#ffffff',
    borderLeft: cssProps.value['border'],
    borderRight: cssProps.value['border'],
    borderBottom: cssProps.value['border'],
    borderRadius: '0 0 4px 4px',
    boxShadow: cssProps.value['boxShadow'],
    zIndex: (parseInt(cssProps.value['zIndex'] || '0') + 1).toString(),
  }
})

// 打开/关闭下拉框
const toggleDropdown = () => {
  if (props.DomItem['enabled']) {
    isOpen.value = !isOpen.value
  }
  console.log('toggleDropdown', isOpen.value);

}

// 选择项目
const onSelect = (val) => {
  currentValue.value = val
  isOpen.value = false
}

// 点击外部关闭下拉框
const closeDropdown = (e) => {
  const selectEl = document.querySelector('.select-wrapper')
  if (selectEl && !selectEl.contains(e.target)) {
    isOpen.value = false
  }
}

// 双向绑定到平台数据
watch(currentValue, (val) => {
  xSetValue(props.DomItem['value'], val)
})

onMounted(() => {
  document.addEventListener('click', closeDropdown)
})

onUnmounted(() => {
  document.removeEventListener('click', closeDropdown)
})
</script>

<script lang="ts">
export default {
  name: 'FWSelect'
}
</script>

<style lang="scss" scoped>
.select-wrapper {
  position: relative;
  background-color: var(--basic-background-color);
  color: var(--basic-color);
  transform: var(--basic-transform);
  font-size: var(--basic-font-size);
  border: var(--basic-border);
  border-radius: var(--basic-border-radius);
  box-shadow: var(--basic-boxShadow);
}

.select-display {
  cursor: pointer;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.select-display-label {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.select-arrow {
  display: inline-block;
  width: 18px;
  height: 18px;
  margin-left: 5px;
  margin-right: 10px;
  transition: transform 0.2s ease;
  vertical-align: middle;
  color: currentColor;
  font-size: 18px;
}

.is-open .select-arrow {
  transform: rotate(180deg);
}

.select-dropdown {
  position: absolute;
  left: 0;
  top: 100%;
}

.select-item {
  padding: 3px 10px;
  cursor: pointer;
  line-height: normal;
  
  &:hover {
    background-color: rgba(0,0,0,0.05);
  }
  
  &.selected {
    background-color: rgba(43, 107, 255, 0.1);
  }
}
</style>
