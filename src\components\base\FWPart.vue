<template>
	<div :style="partStyle" class="xtest part" @click.stop="handleClick">
		<ComponentMgr 
			v-for="(xItem, index) in children" 
			:key="index"  
			:DomItem="xItem" 
			:PathPrefix="childPathPrefix(index)" 
			:ItemDataPath="props.ItemDataPath"
			:ComponentPath="props.DomItem['path']"
		/>
	</div>
</template>

<script lang="ts" setup>
	import { reactive, inject, onMounted, useAttrs, toRaw, computed } from 'vue';
	import { useComponent } from '../../frame/useComponent.js';
    import ComponentMgr from '../ComponentMgr.vue';
	import { getPart } from '../../axios/dao/api.js'
	
	// 定义Props类型
	interface Props {
		DomItem?: {
			css?: Record<string, any>;
			children?: any[];
			onClick?: any;
			id?: string | number;
			partId?: string | number;
			param?: Record<string, any>;
			[key: string]: any;
		};
		PathPrefix?: string;
		ItemDataPath?: string;
	}
	
	const props = useAttrs() as Props;
	const { xRefCss, xOnClick, xRefValue, xSetChildren } = useComponent(props);
	
	// 计算属性：样式
	// const partStyle = computed(() => xRefCss(props.DomItem?.css));
	const partStyle = computed(() => {
		const css = xRefCss(props.DomItem?.css) || {};
		return {
			overflow:"hidden",
			position: "relative",
			width: "inherit",
			...css,
			
		};
	});
	
	// 计算属性：子组件
	const children = computed(() => props.DomItem?.children || []);
	
	// 方法：生成子组件路径前缀
	const childPathPrefix = (index: number) => {
		return `${props.PathPrefix || ''}.children.${index}`;
	};
	
	// 点击处理函数
	const handleClick = () => {
		if (props.DomItem?.onClick) {
			xOnClick(props.DomItem.onClick);
		}
	};
	
	onMounted(() => {
		const targetStr = "ref:PageDom.[" + props.DomItem?.id + "].children";
		const partId = props.DomItem?.partId;
		const param = toRaw(props.DomItem?.param || {});
		// loadPart(targetStr, partId, param);
	});
	
	async function loadPart(target, partId, param) {
		let ptParam = {
			partId: partId,
			...param,
		};
		const res = await getPart(ptParam);
		if (res.code == 200) {
			xSetChildren(target, res.data.PageDom, 'replace');
		}
	}
</script>

<script lang="ts">
export default {
  name: 'FWPart'
}
</script>

<style lang="scss">

</style>