import axios from "axios";
import { useGlobalData} from '../pinia/globalData';
// import { ElMessage } from "element-plus";
const global = useGlobalData()

// 处理 session 无效的函数
function handleSessionInvalid() {
  // 清空 session 和本地存储
  global.clearSession()

  // 跳转到主页并清空页面栈
  window.location.href = '/'
}
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;

// 创建 Axios 实例
const http = axios.create({
  baseURL: apiBaseUrl,//process.env.NODE_ENV === "development" ? "devApi" : "proApi",
  timeout: 5000, // 请求超时时间
  headers: {
    "Content-Type": "application/json",
  },
});

// 请求拦截器
http.interceptors.request.use(
  (config) => {
    // 在发送请求之前做些什么，例如添加 token
	  config.headers.authorization = global.getSession();
	  config.headers.client = "PC"
	  config.headers.appid = global.getAppId()
      return config;
  },
  (error) => {
    // 对请求错误做些什么
    return Promise.reject(error);
  }
);

// 响应拦截器
http.interceptors.response.use(
  (response) => {
    // 对响应数据做些什么
    if (response.data.code === 200) {
      return Promise.resolve(response.data);
    } else if (response.data.code === 5502) {
      // 处理 session 无效的情况
      console.log('Session 无效，跳转到主页');
      handleSessionInvalid();
      return Promise.reject(response.data);
    } else {
      // code不等于200弹出msg
      return Promise.reject(response.data);
    }
  },
  (error) => {
    // 对响应错误做些什么
    return Promise.reject(error);
  }
);

export default http;

